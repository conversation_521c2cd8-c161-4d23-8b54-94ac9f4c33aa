package com.king.cloudpay.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.king.cloudpay.core.aop.Excel;
import com.king.cloudpay.core.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 退款订单表
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_refund_order")
public class RefundOrder extends BaseModel<RefundOrder> {

    public static final byte STATE_INIT = 0; //订单生成
    public static final byte STATE_ING = 1; //退款中
    public static final byte STATE_SUCCESS = 2; //退款成功
    public static final byte STATE_FAIL = 3; //退款失败
    public static final byte STATE_CLOSED = 4; //退款任务关闭

    public static final LambdaQueryWrapper<RefundOrder> gw() {
        return new LambdaQueryWrapper<>();
    }

    private static final long serialVersionUID = 1L;

    /**
     * 退款订单号（支付系统生成订单号）
     */
    @TableId
    private String refundOrderId;

    /**
     * 支付订单号（与t_pay_order对应）
     */
    private String payOrderId;

    /**
     * 渠道支付单号（与t_pay_order channel_order_no对应）
     */
    private String channelPayOrderNo;

    /**
     * 商户号
     */
    private String mchNo;

    /**
     * 卡商好
     */
    private String cardNo;

    /**
     * 服务商号
     */
    private String isvNo;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 类型: 1-普通商户, 2-特约商户(服务商模式)
     */
    private Byte mchType;

    /**
     * 商户退款单号（商户系统的订单号）
     */
    private String mchRefundNo;

    /**
     * 支付方式代码
     */
    private String wayCode;

    /**
     * 支付接口代码
     */
    private String ifCode;

    /**
     * 支付金额,单位分
     */
    private Long payAmount;

    /**
     * 实际支付金额,单位分
     */
    @Excel(name = "实际支付金额", pToYuan = true)
    private Long practicalAmount;

    /**
     * 退款金额,单位分
     */
    private Long refundAmount;

    /**
     * 三位货币代码,人民币:cny
     */
    private String currency;

    /**
     * 商户手续费费率快照
     */
    @Excel(name = "商户手续费费率快照")
    private BigDecimal mchFeeRate;

    /**
     * 商户手续费,单位分
     */
    @Excel(name = "商户手续费", pToYuan = true)
    private Long mchFeeAmount;

    /**
     * 退款状态:0-订单生成,1-退款中,2-退款成功,3-退款失败,4-退款任务关闭
     */
    private Byte state;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 渠道订单号
     */
    private String channelOrderNo;

    /**
     * 渠道错误码
     */
    private String errCode;

    /**
     * 渠道错误描述
     */
    private String errMsg;

    /**
     * 特定渠道发起时额外参数
     */
    private String channelExtra;

    /**
     * 通知地址
     */
    private String notifyUrl;

    /**
     * 扩展参数
     */
    private String extParam;

    /**
     * 订单退款成功时间
     */
    private Date successTime;

    /**
     * 退款失效时间（失效后系统更改为退款任务关闭状态）
     */
    private Date expiredTime;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 卡商收款凭证上传状态 0 未上传 1已上传
     */
    private Byte cardCertificateState;

    /**
     * 卡商收款凭证
     */
    private String cardCertificateImg;
    /**
     * app自动付款时绑定的银行卡号
     */
    private String bankAccountId;

    /**
     * 收款人手机号
     */
    @Excel(name = "收款人手机号")
    private String phoneNumber;

    /**
     * 如果这笔订单是 卡商 app 短信自动识别的，绑定那条短信识别成功短信ID
     */
    private Long smsId;

    /**
     * 商户代收到了哪张银行卡
     */
    @TableField(exist = false)
    private BankAccount bankAccount;

    /**
     * app 自动代收匹配中的短信信息
     */
    @TableField(exist = false)
    private TransactionSms transactionSms;

    // Manual getter method for Lombok compatibility
    // This ensures the method is available even if Lombok processing fails
    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

}
